package com.fuyingedu.watch.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fuyingedu.watch.comm.exception.NothingValueException;
import com.fuyingedu.watch.entity.Account;
import com.fuyingedu.watch.entity.BodyRecord;
import com.fuyingedu.watch.mapper.AccountMapper;
import com.fuyingedu.watch.mapper.BodyRecordMapper;
import com.fuyingedu.watch.model.CommResp;
import com.fuyingedu.watch.model.RespMeta;
import com.fuyingedu.watch.model.account.AccountConvertor;
import com.fuyingedu.watch.model.account.InitReq;
import com.fuyingedu.watch.model.account.UpdateProfileReq;
import com.fuyingedu.watch.model.account.UpdateBodyRecordReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.List;

@Service
@Slf4j
public class AccountService {

    @Autowired
    private AccountMapper accountMapper;
    @Autowired
    private BodyRecordMapper bodyRecordMapper;

    @Transactional(rollbackFor = Exception.class)
    public CommResp<?> init(Long uid, InitReq req) {
        // 检查是否已存在该uid的账号
        LambdaQueryWrapper<Account> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Account::getId).eq(Account::getUid, uid).last("limit 1");
        Account existingAccount = accountMapper.selectOne(queryWrapper);
        
        if (existingAccount != null) {
            return CommResp.warning("该用户已存在账号");
        }
        
        // 创建新账号
        Account account = AccountConvertor.toAccount(req);
        account.setUid(uid);
        accountMapper.insert(account);
        
        // 添加初始身高体重记录
        BodyRecord bodyRecord = new BodyRecord();
        bodyRecord.setAccountId(account.getId());
        bodyRecord.setHeight(req.getHeight());
        bodyRecord.setWeight(req.getWeight());
        bodyRecord.setCreatedAt(LocalDateTime.now());
        bodyRecordMapper.insert(bodyRecord);
        return CommResp.success();
    }

    @Transactional(rollbackFor = Exception.class)
    public CommResp<?> updateProfile(Long uid, UpdateProfileReq req) {
        // 检查是否存在该uid的账号
        LambdaQueryWrapper<Account> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Account::getId).eq(Account::getUid, uid).eq(Account::getId, req.getId());
        Account existingAccount = accountMapper.selectOne(queryWrapper);
        
        if (existingAccount == null) {
            return CommResp.warning("用户账号不存在");
        }
        
        // 更新用户资料
        Account updateAccount = new Account();
        updateAccount.setId(req.getId());
        boolean needUpdate = false;
        if (req.getNickname() != null) {
            updateAccount.setNickname(req.getNickname());
            needUpdate = true;
        }
        if (req.getAvatar() != null) {
            updateAccount.setAvatar(req.getAvatar());
            needUpdate = true;
        }
        if (req.getSlogan() != null) {
            updateAccount.setSlogan(req.getSlogan());
            needUpdate = true;
        }
        
        if (!needUpdate) {
            return CommResp.warning("没有需要更新的信息");
        }
        
        updateAccount.setUpdatedAt(LocalDateTime.now());
        accountMapper.updateById(updateAccount);
        
        return CommResp.success();
    }
    
    @Transactional(rollbackFor = Exception.class)
    public CommResp<?> updateBodyRecord(Long uid, UpdateBodyRecordReq req) {
        // 判空
        if (req.getHeight() == null && req.getWeight() == null) {
            return CommResp.warning("没有需要更新的信息");
        }
        // 检查是否存在该uid的账号
        LambdaQueryWrapper<Account> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Account::getId, Account::getHeight, Account::getWeight)
                .eq(Account::getUid, uid).eq(Account::getId, req.getId());
        Account existingAccount = accountMapper.selectOne(queryWrapper);
        
        if (existingAccount == null) {
            return CommResp.warning("用户账号不存在");
        }
        // 如果参数为空设置原来的值
        if (req.getHeight() == null) {
            req.setHeight(existingAccount.getHeight());
        }
        if (req.getWeight() == null) {
            req.setWeight(existingAccount.getWeight());
        }
        
        // 更新账号中的身高体重信息
        Account updateAccount = new Account();
        updateAccount.setId(req.getId());
        updateAccount.setHeight(req.getHeight());
        updateAccount.setWeight(req.getWeight());
        accountMapper.updateById(updateAccount);
        
        // 添加身高体重记录
        BodyRecord bodyRecord = new BodyRecord();
        bodyRecord.setAccountId(req.getId());
        bodyRecord.setHeight(req.getHeight());
        bodyRecord.setWeight(req.getWeight());
        bodyRecordMapper.insert(bodyRecord);
        
        return CommResp.success();
    }
}