package com.fuyingedu.watch.controller;

import com.fuyingedu.watch.comm.interceptor.Login;
import com.fuyingedu.watch.model.CommResp;
import com.fuyingedu.watch.model.account.InitReq;
import com.fuyingedu.watch.model.account.UpdateProfileReq;
import com.fuyingedu.watch.service.AccountService;
import com.fuyingedu.watch.model.account.UpdateBodyRecordReq;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/watch/account")
public class AccountController {

    @Autowired
    private AccountService accountService;

    @Login
    @PostMapping("init")
    public CommResp<?> init(
            @Login Long uid,
            @RequestBody @Valid InitReq req
    ) {
        return accountService.init(uid, req);
    }
    
    @Login
    @PostMapping("updateProfile")
    public CommResp<?> updateProfile(
            @Login Long uid,
            @RequestBody @Valid UpdateProfileReq req
    ) {
        return accountService.updateProfile(uid, req);
    }
    
    @Login
    @PostMapping("updateBodyRecord")
    public CommResp<?> updateBodyRecord(
            @Login Long uid,
            @RequestBody @Valid UpdateBodyRecordReq req
    ) {
        return accountService.updateBodyRecord(uid, req);
    }
}